package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.enums.RuleTypeEnum;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.crm.recycling.task.executor.util.Pair;
import com.facishare.crm.recycling.task.executor.util.SFAAuditLogRecalculateConvert;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description 客户到期时间重算
 * <AUTHOR>
 * @Date 2019-02-25 17:03
 */

@Component
@Slf4j
public class AccountRecalculate extends AbstractRecalculate {


    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;





    @SFAAuditLog(bizName = "#bizName", entityClass = RecalculateMessage.class, convertClass = SFAAuditLogRecalculateConvert.class, status = "#status",
            messageId = "#msg.msgId", extra = "#msg.reconsumeTimes",cost1 = "#cost1",extra1 = "#extra1",extra2 = "#extra2")
    @Override
    public void execute(RecalculateMessage message) {
        super.execute(message);
        if (message.getObjectData() == null && !message.getActionCode().equals(ActionCodeEnum.CHANGE_RULE.getActionCode())
                && !message.getActionCode().equals(ActionCodeEnum.CHANGE_REMIND.getActionCode())){
            return;
        }
        switch (ActionCodeEnum.actionCodeOf(message.getActionCode())) {
            case CHANGE_RULE:
                newRecalculateByRule(message);
                break;
            case CHANGE_REMIND:
                newRecalculateByChangeRemind(message);
                break;
            case INVALID:
                newRecalculateByInvalid(message);
                break;
            case EXTEND:
                newRecalculateExtend(message);
                break;
            case MOVE:
                recalculateByMove(message);
                break;
            case RETURN:
                recalculateBySendBack(message);
                break;
            case CHANGE_DEAL:
                recalculateByDeal(message);
                break;
            case RECOVER:
            case CHANGE_OWNER:
            case ALLOCATE:
            default:
                recalculate(message);
                break;
        }
    }


    /**
     * 转移客户
     *
     * @param message
     */
    public void recalculateByMove(RecalculateMessage message) {
        IObjectData objectData = message.getObjectData();
        //移除公海并分配负责人，根据负责人回收规则重新计算到期时间
        // 不为空，转移到公海，根据新公海回收规则重新计算到期时间
        log.info("recalculateByMove，remove highSea calculate by owner ：{},{},{}", message.getTenantId(), objectData.getId(), objectData.getName());
        if (Boolean.TRUE.equals(isNeedRecalculate(objectData))) {
            // 重算到期时间
            recalculate(message);
        }
    }

    /**
     * 退回客户
     * 清空到期时间
     *
     * @param message
     */
    public void recalculateBySendBack(RecalculateMessage message) {
        if (Boolean.TRUE.equals(isNeedRecalculate(message.getObjectData()))){
            recalculate(message);
        }else{
            delete(message.getTenantId(), message.getObjectId());
            customerBiz.clearExpireTime(message.getObjectData());
        }
    }

    /**
     * 更改用户成交状态
     *
     * @param message
     */
    public void recalculateByDeal(RecalculateMessage message) {
        recalculate(message);
    }

    private void newRecalculateByInvalid(RecalculateMessage message) {
        if (LifeStatusEnum.INVALID.getValue().equals(message.getObjectData().get(LIFE_STATUS))) {
            log.info("newRecalculateByInvalid objectData status is:{},{}", message.getObjectId(),
                    message.getObjectData().get(LIFE_STATUS));
            customerBiz.clearExpireTime(message.getObjectData());
            delete(message.getTenantId(),message.getObjectData().getId());
        }
    }


    private void recalculate(RecalculateMessage message){
        IObjectData objectData = message.getObjectData();
        log.info("newRecalculate 计算客户到期时间:{},{},{}", objectData.getTenantId(), objectData.getName(), objectData.getId());
        recalculateNewCustomer(objectData.getTenantId(),objectData,null,null,null);
        SFALogContext.putVariable("status", true);
    }

    private void newRecalculateExtend(RecalculateMessage message){
        IObjectData objectData = message.getObjectData();
        log.info("newRecalculate 计算客户申请延期到期时间:{},{},{}", objectData.getTenantId(), objectData.getName(), objectData.getId());
        if (Boolean.FALSE.equals(isNeedRecalculate(objectData))) {
            log.info("newRecalculate isNormalCustomer id:{} life_status:{},biz_status:{}",objectData.getId(), objectData.get(LIFE_STATUS), objectData.get(BIZ_STATUS));
            return;
        }
        recalculateNewCustomer(objectData.getTenantId(),objectData,null,null,message.getExtendDays());
    }

    private void newRecalculateByRule(RecalculateMessage message){
        log.info("newRecalculateByRule AccountObj recalculateByRule:{} ", message);
        String tenantId = message.getTenantId();
        String dataId = message.getObjectId();
        //公海
        if (message.getHighSeas() != null && message.getHighSeas()) {
            List<RecyclingRuleInfoModel> recyclingRules = recyclingBiz.getRecyclingRule(tenantId, dataId, HIGH_SEAS_OBJ);
            log.info("recalculateByRule highSeas loop :{}", message);
            loopNewCalculateHighSeas(tenantId, dataId, recyclingRules);
            //非公海
        } else {
            List<String> deptIds = new ArrayList<>();
            //新版本增加了批量部门id参数
            if (CollectionUtils.isEmpty(message.getDeptIds())) {
                deptIds.add(dataId);
            } else {
                deptIds = message.getDeptIds();
            }
            loopNewCalculateDepts(tenantId,deptIds);
        }
    }

    private void loopNewCalculateHighSeas(String tenantId,String HighSeasId,List<RecyclingRuleInfoModel> recyclingRules){
        List<RemindRuleModel> remindRules = recyclingBiz.getRemindRulesByDataId(tenantId,HighSeasId);
        List<String> idLists = customerBiz.getAllAccountIdsByhighSeasId(tenantId, HighSeasId);
        if (CollectionUtils.isEmpty(idLists)){
            return;
        }
        log.info("recalculateByRule highSeas loop highSeasId:{},starg tenantId:{},totalSize:{}",HighSeasId,tenantId,idLists.size());
        List<IObjectData> objectDataList;
        int pageSize = 10; // 每页的大小
        int pageCount = (int) Math.ceil(idLists.size() / (double) pageSize); // 总页数
        for (int i = 0; i < pageCount; i++) {
            int fromIndex = i * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, idLists.size());
            List<String> idPageList = idLists.subList(fromIndex, toIndex);
            objectDataList = customerBiz.getObjectByIds(tenantId, idPageList, ACCOUNT_OBJ);
            if (CollectionUtils.isEmpty(objectDataList)) {
                log.info("this highSeas no customer {}", HighSeasId);
                continue;
            }
            log.info("recalculateByRule highSeas loop highSeasId:{}, doing fromIndex:{},toIndex:{},idPageList:{}",HighSeasId,fromIndex,toIndex,idPageList);
            for (IObjectData objectData : objectDataList) {
                if (Boolean.TRUE.equals(recyclingTaskGray.toSlowRecyclingTenantId(tenantId))){
                    sfaRecyclingTaskRateLimiterService.getCrmSlowRecyclingLimiter().acquire();
                }else {
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                }
                recalculateNewCustomer(tenantId,objectData,recyclingRules,remindRules,null);
            }
        }
        log.info("recalculateByRule highSeas loop highSeasId::{} end,tenantId:{} accountSize:{}",HighSeasId,tenantId,idLists.size());
    }

    private void loopNewCalculateDepts(String tenantId,List<String> objectIds){
an        List<String> memberLists = customerBiz.getMembersByDeptIds(tenantId, objectIds);
        if (CollectionUtils.isEmpty(memberLists)){
            return;
        }
        Set<String> members = Sets.newHashSet(memberLists);
        log.info("loopNewCalculateDepts begin tenantId:{},deptId:{},members:{},members:{}",tenantId,objectIds,members.size(),members);
        Map<String,List<RecyclingRuleInfoModel>> ruleCacheMap = new HashMap<>();
        Map<String,List<RemindRuleModel>> remindruleCacheMap = new HashMap<>();
        List<RecyclingRuleInfoModel> recyclingRules;
        List<RemindRuleModel> remindRules;
        for (String member : members) {
            //  查询部门下员工是否在非公海的规则配置里面
            String deptId = customerBiz.getDept(tenantId, member);
            // map缓存下循环计算的规则
            recyclingRules = recyclingBiz.getCacheRecyclingRule(tenantId,ruleCacheMap,deptId,HIGH_SEAS_OBJ);
            remindRules = recyclingBiz.getCacheRemindRule(tenantId,remindruleCacheMap,deptId);
            List<String> idLists = customerBiz.getAllAccountIdsByOwnerId(tenantId, member);
            log.info("loopNewCalculateDepts by member start, tenantId:{},deptId:{},size:{},member:{},",tenantId,deptId,idLists.size(),member);
            if (CollectionUtils.isEmpty(idLists)){
                continue;
            }
            List<IObjectData> objectDataList;
            int pageSize = 10; // 每页的大小
            int pageCount = (int) Math.ceil(idLists.size() / (double) pageSize); // 总页数
            for (int i = 0; i < pageCount; i++) {
                int fromIndex = i * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, idLists.size());
                List<String> idPageList = idLists.subList(fromIndex, toIndex);
                objectDataList = customerBiz.getObjectByIds(tenantId, idPageList, ACCOUNT_OBJ);
                log.info("loopNewCalculateDepts by member doing tenantId:{},deptId:{},member:{},idList:{},fromIndex:{},toIndex:{},idPageList:{}",
                        tenantId,deptId,member,idLists.size(),fromIndex,toIndex,idPageList);
                for (IObjectData objectData : objectDataList) {
                    if (Boolean.TRUE.equals(recyclingTaskGray.toSlowRecyclingTenantId(tenantId))){
                        sfaRecyclingTaskRateLimiterService.getCrmSlowRecyclingLimiter().acquire();
                    }else {
                        sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                    }
                    if (Boolean.TRUE.equals(ObjectDataUtils.isHighSeas(objectData))){
                        continue;
                    }
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                    try {
                        recalculateNewCustomer(tenantId,objectData,recyclingRules,remindRules,null);
                    } catch (Exception e) {
                        log.warn("recalculate rule, recalculateNewCustomer error,tenantId:{},objectId:{}",tenantId,objectData.getId());
                    }
                }
            }
            log.info("loopNewCalculateDepts by member end, tenantId:{},deptId:{},size:{},member:{},",tenantId,deptId,idLists.size(),member);
        }
        log.info("loopNewCalculateDepts by member depts end, tenantId:{},deptId:{},membersSize:{},member:{} ",tenantId,objectIds,members.size(),members);
    }


    private void newRecalculateByChangeRemind(RecalculateMessage message) {
        log.info("newRecalculateByChangeRemind:{}",message);
        String objectId = message.getObjectId();
        String tenantId = message.getTenantId();
        if (tenantId == null){
            return;
        }

        //公海
        if ((message.getHighSeas() == null &&  message.getObjectId().length() > 7)
                || (message.getHighSeas()!=null && message.getHighSeas())){
            List<RecyclingRuleInfoModel> recyclingRules = recyclingBiz.getRecyclingRule(tenantId,objectId,HIGH_SEAS_OBJ);
            loopNewCalculateRemindsHighSeas(message,recyclingRules);
            //非公海
        }else {
            List<String> deptIds = new ArrayList<>();
            //新版本增加了批量部门id参数
            if (CollectionUtils.isEmpty(message.getDeptIds())){
                deptIds.add(objectId);
            }else {
                deptIds = message.getDeptIds();
            }
            loopNewCalculateRemindsDepts(message,deptIds);
        }
    }
    private void loopNewCalculateRemindsHighSeas(RecalculateMessage message,List<RecyclingRuleInfoModel> recyclingRules) {
        String objectId = message.getObjectId();
        String tenantId = message.getTenantId();
        List<String> idLists = customerBiz.getAllAccountIdsByhighSeasId(tenantId, objectId);
        if (CollectionUtils.isEmpty(idLists)){
            return;
        }
        boolean hasRecyclingRemindRule = false;
        if(CollectionUtils.isNotEmpty(recyclingRules)){
            List<RecyclingRuleInfoModel> reminds = recyclingRules.stream().filter(x -> CollectionUtils.isNotEmpty(x.getRecyclingRemindRuleList())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(reminds)){
                hasRecyclingRemindRule = true;
            }
        }
        List<RemindRuleModel> remindRuleModels = recyclingBiz.getRemindRulesByDataId(tenantId, objectId);
        RuleTypeRemindTime ruleTypeRemindTime = null;

        List<IObjectData> objectDataList;
        int pageSize = 10; // 每页的大小
        int pageCount = (int) Math.ceil(idLists.size() / (double) pageSize); // 总页数

        for (int i = 0; i < pageCount; i++) {
            int fromIndex = i * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, idLists.size());
            List<String> idPageList = idLists.subList(fromIndex, toIndex);
            objectDataList = customerBiz.getObjectByIds(tenantId, idPageList, ACCOUNT_OBJ);

            for (IObjectData objectData : objectDataList) {
                if (LifeStatusEnum.INVALID.getValue().equals(objectData.get(LIFE_STATUS,String.class))){
                    delete(tenantId,objectId);
                    continue;
                }
                if(objectData.get(EXPIRE_TIME) == null){
                    continue;
                }
                List<RuleTypeRemindTime> remindTimes = getNewRemindTimes(objectData, remindRuleModels);
                if (CollectionUtils.isEmpty(remindTimes)){
                    deleteRemind(tenantId,objectData.getId());
                    continue;
                }
                if (hasRecyclingRemindRule){
                    ruleTypeRemindTime = getRecyclingRemindTime(recyclingRules,objectData);
                    remindTimes.add(ruleTypeRemindTime);
                }
                RemindMessage newRemindMessage = RemindMessage.builder()
                        .tenantId(tenantId)
                        .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                        .objectId(objectData.getId())
                        .ruleTypeRemindTimes(remindTimes)
                        .build();

                log.info("loopCalculateRemindsHighSeas batchRecalculateRemind :{}", newRemindMessage);
                sendRemindTask(newRemindMessage);
            }
        }
        log.info("loopCalculateHighSeas end, tenantId:{} highSeasId:{},idListsSize:{} ", tenantId,objectId,idLists.size());
    }

    private void loopNewCalculateRemindsDepts(RecalculateMessage message,List<String> deptIds){
        String tenantId = message.getTenantId();
        List<String> memberLists = customerBiz.getMembersByDeptIds(tenantId, deptIds);
        if (CollectionUtils.isEmpty(memberLists)){
            return;
        }
        Set<String> members = Sets.newHashSet(memberLists);
        log.info("loopNewCalculateRemindsDepts:{},members:{}",tenantId,members);
        Map<String,List<RecyclingRuleInfoModel>> ruleCacheMap = new HashMap<>();
        Map<String,List<RemindRuleModel>> remindruleCacheMap = new HashMap<>();
        List<RecyclingRuleInfoModel> recyclingRules;
        List<RemindRuleModel> remindRules;
        for (String member : members) {
            String deptId = customerBiz.getDept(tenantId, member);
            List<String> idLists = customerBiz.getAllAccountIdsByOwnerId(tenantId, member);
            if (CollectionUtils.isEmpty(idLists)){
                log.info("loopNewCalculateRemindsDepts:{} by member end,deptId:{},member:{},objectData is null",tenantId,deptId,member);
                continue;
            }
            log.info("loopNewCalculateRemindsDepts:{} start,{},{},idListsSize:{}",message.getTenantId(),member,deptId,idLists.size());
            remindRules = recyclingBiz.getCacheRemindRule(tenantId, remindruleCacheMap, deptId);
            recyclingRules = recyclingBiz.getCacheRecyclingRule(tenantId,ruleCacheMap,deptId,HIGH_SEAS_OBJ);
            int pageSize = 10; // 每页的大小
            int pageCount = (int) Math.ceil(idLists.size() / (double) pageSize); // 总页数

            for (int i = 0; i < pageCount; i++) {
                int fromIndex = i * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, idLists.size());
                List<String> idPageList = idLists.subList(fromIndex, toIndex);
                List<IObjectData> objectDataList = customerBiz.getObjectByIds(tenantId, idPageList, ACCOUNT_OBJ);

                log.info("loopNewCalculateRemindsDepts:{} do :{},{},fromIndex:{},toIndex:{},idPageList:{}",message.getTenantId(),member,deptId,fromIndex,toIndex,idPageList);
                for (IObjectData objectData : objectDataList) {
                    calcualteRemind(recyclingRules,remindRules,objectData);
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                }
            }
            log.info("loopNewCalculateRemindsDepts end:{},{},idListsSize:{}",message.getTenantId(),member,idLists.size());
        }
    }

    /**
     *
     * @param tenantId
     * @param objectData
     * @param recyclingRules
     * @param remindRules
     * @param extendDays 申请延期天数
     */
    public void recalculateNewCustomer(String tenantId, IObjectData objectData,List<RecyclingRuleInfoModel> recyclingRules,List<RemindRuleModel> remindRules,Double extendDays){
        if (objectData == null){
            return;
        }
        log.info("recalculateNewCustomer:{},{},{}", tenantId, objectData.getName(), objectData.getId());
        if (Boolean.FALSE.equals(isNeedRecalculate(objectData))) {
            log.info("recalculateNewCustomer isNormalCustomer id:{} life_status:{},biz_status:{}",objectData.getId(), objectData.get(LIFE_STATUS), objectData.get(BIZ_STATUS));
            return;
        }
        sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
        if (CollectionUtils.isEmpty(recyclingRules)){
            recyclingRules = recyclingBiz.getRecyclingRule(objectData, HIGH_SEAS_OBJ);
            if (CollectionUtils.isEmpty(recyclingRules)) {
                log.info("recyclingRules is null clearExpireTime:{}",objectData.getId());
                customerBiz.clearExpireTime(objectData);
                deleteRecycling(objectData.getTenantId(),objectData.getId());
            }
        }
        // 提醒实例对象
        RuleTypeRemindTime recyclingRemind = RuleTypeRemindTime.builder().build();
        Pair<Date, Date> datePair = calculateExpireTimeByNewRule(tenantId,ACCOUNT_OBJ,objectData,recyclingRules,recyclingRemind,extendDays);
        // 无匹配的回收规则，公海客户
        if (datePair == null && ObjectDataUtils.isHighSeas(objectData)){
            deleteRemind(tenantId,objectData.getId());
            return;
        }
        List<RuleTypeRemindTime> remindTimes = getNewRemindTimes(objectData,remindRules);
        if (datePair != null && datePair.getValue() == null && CollectionUtils.isEmpty(remindTimes)){
            deleteRemind(tenantId,objectData.getId());
            return;
        }
        if (recyclingRemind.getRemindTime() != null){
            remindTimes.add(recyclingRemind);
        }
        SFALogContext.putVariable("status", false);
        log.info(" recalculateNewCustomer datePair {}  remindTimes:{} ,objectId:{}",datePair,remindTimes,objectData.getId());
        RemindMessage remindMessage = RemindMessage.builder()
                .tenantId(tenantId)
                .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .objectId(objectData.getId())
                .ruleTypeRemindTimes(remindTimes)
                .build();
        sendRemindTask(remindMessage);
    }

    /**
     *
     * @param objectData
     * @param remindRules null 会去查数据库，空对象不会去查库
     * @return
     */
    public List<RuleTypeRemindTime> getNewRemindTimes(IObjectData objectData,List<RemindRuleModel> remindRules) {

        // 领取时间 claimed_time
        Long claimedTime = objectData.get(CLAIMED_TIME) == null ? 0L : Long.parseLong(objectData.get(CLAIMED_TIME).toString());
        // 创建时间 create_time
        Long createTime = objectData.get(CREATE_TIME) == null ? 0L : Long.parseLong(objectData.get(CREATE_TIME).toString());
        // 负责人变更时间 owner_modified_time
        Long ownrChangeTime = objectData.get(OWNER_MODIFIED_TIME) == null ? 0L : Long.parseLong(objectData.get(OWNER_MODIFIED_TIME).toString());

        List<Long> lastedTime = new ArrayList<>();
        lastedTime.add(claimedTime);
        lastedTime.add(createTime);
        lastedTime.add(ownrChangeTime);
        lastedTime = lastedTime.stream().sorted().collect(Collectors.toList());

        Long lastFollowTime;
        Long lastDealClosedTime;

        // 如果最后跟进时间为空，跟进提醒的时间以领取时间、创建时间、负责人变更时间 的最晚时间为准
        if (objectData.get(LAST_FOLLOWED_TIME) == null) {
            lastFollowTime = lastedTime.get(2);
        } else {
            lastFollowTime = Long.valueOf(objectData.get(LAST_FOLLOWED_TIME).toString());
        }

        // 如果最后成交时间为空，跟进提醒的时间以领取时间、创建时间、负责人变更时间 的最晚时间为准
        if (objectData.get(LAST_DEAL_CLOSED_TIME) == null) {
            lastDealClosedTime = lastedTime.get(2);
        } else {
            lastDealClosedTime = Long.valueOf(objectData.get(LAST_DEAL_CLOSED_TIME).toString());
        }

        // 只有是null才会查库
        if (remindRules == null){
            remindRules = recyclingBiz.getRemindRules(objectData);
            log.info("getNewRemindTimes remindRules:{},{}",remindRules == null ? "null": remindRules.size(),objectData.getId());
        }
        List<RuleTypeRemindTime> highSeasRemindTimes = new ArrayList<>();
        if (recyclingTaskGray.expireTimeSkipHolidays(objectData.getTenantId())){
            for (RemindRuleModel remindRule : remindRules) {
                Integer dealDays = remindRule.getDealDays();
                Integer followUpDays = remindRule.getFlowUpDays();
                RuleTypeRemindTime highSeasRemindTime = new RuleTypeRemindTime();
                if (dealDays == 0) {
                    highSeasRemindTime.setRuleType(RuleTypeEnum.FOLLOW_UP_DAYS_REMIND.getValue());
                    Long remindTime = DateUtils.afterDays(lastFollowTime, followUpDays);;
                    if (remindRule.getSkipHolidays()) {
                        remindTime = getRemainingDays(new Date(lastFollowTime), followUpDays, objectData);
                    }
                    highSeasRemindTime.setRemindTime(remindTime);
                    highSeasRemindTime.setDays(followUpDays);
                    highSeasRemindTimes.add(highSeasRemindTime);
                } else {
                    highSeasRemindTime.setRuleType(RuleTypeEnum.DEAL_DAYS_REMIND.getValue());
                    Long remindTime = DateUtils.afterDays(lastDealClosedTime, dealDays);
                    if (remindRule.getSkipHolidays()) {
                        getRemainingDays(new Date(lastDealClosedTime),dealDays, objectData);
                    }
                    highSeasRemindTime.setRemindTime(remindTime);
                    highSeasRemindTime.setDays(dealDays);
                    highSeasRemindTimes.add(highSeasRemindTime);
                }
            }
        }else {
            for (RemindRuleModel remindRule : remindRules) {
                Integer dealDays = remindRule.getDealDays();
                Integer followUpDays = remindRule.getFlowUpDays();
                RuleTypeRemindTime highSeasRemindTime = new RuleTypeRemindTime();
                if (dealDays == 0) {
                    highSeasRemindTime.setRuleType(RuleTypeEnum.FOLLOW_UP_DAYS_REMIND.getValue());
                    highSeasRemindTime.setRemindTime(DateUtils.afterDays(lastFollowTime, followUpDays));
                    highSeasRemindTime.setDays(followUpDays);
                    highSeasRemindTimes.add(highSeasRemindTime);
                } else {
                    highSeasRemindTime.setRuleType(RuleTypeEnum.DEAL_DAYS_REMIND.getValue());
                    highSeasRemindTime.setRemindTime(DateUtils.afterDays(lastDealClosedTime, dealDays));
                    highSeasRemindTime.setDays(dealDays);
                    highSeasRemindTimes.add(highSeasRemindTime);
                }
            }
        }
        if ("700146".equals(objectData.getTenantId())){
            log.info("getNewRemindTimes remindRules:700146 {},{}",objectData.getId(),remindRules);
        }
        return highSeasRemindTimes.stream()
                .sorted(Comparator.comparing(RuleTypeRemindTime::getRemindTime)).collect(Collectors.toList());
    }


    /**
     * 获取回收规则 的提前提醒天数的时间
     * @param recyclingRules
     * @param objectData
     * @return
     */
    private RuleTypeRemindTime getRecyclingRemindTime(List<RecyclingRuleInfoModel> recyclingRules,
                                                    IObjectData objectData){
        RuleTypeRemindTime ruleTypeRemindTime = RuleTypeRemindTime.builder().build();
        String ruleId = getMappingRuleId(recyclingRules, objectData, ACCOUNT_OBJ);
        // 没有回收规则 或者 没有回收时间的，直接返回
        if (ruleId == null || objectData.get(EXPIRE_TIME) == null){
            log.info("getRecyclingRemindTime no mapping rule:{},id:{}",objectData.getTenantId(),objectData.getId());
            return ruleTypeRemindTime;
        }
        Optional<RecyclingRuleInfoModel> rule = recyclingRules.stream().filter(x -> x.getId().equals(ruleId)).findFirst();
        if (rule.isPresent()){
            RecyclingRuleInfoModel recyclingRule = rule.get();
            Date remindTime = newCalculateRemindTime(recyclingRule,
                    new Date(objectData.get(EXPIRE_TIME,Long.class)),
                    ruleTypeRemindTime,ACCOUNT_OBJ);
            if (remindTime != null){
                ruleTypeRemindTime.setRemindTime(remindTime.getTime());
            }
        }
        return ruleTypeRemindTime;
    }

    private void calcualteRemind(List<RecyclingRuleInfoModel> recyclingRules,List<RemindRuleModel> remindRules,IObjectData objectData){
        List<RecyclingRuleInfoModel> reminds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recyclingRules)){
            reminds = recyclingRules.stream().filter(x -> x.getRecyclingRemindRuleList() != null).collect(Collectors.toList());
        }
        RuleTypeRemindTime ruleTypeRemindTime = RuleTypeRemindTime.builder().build();
        //计算收回规则里面的提醒
        if (CollectionUtils.isNotEmpty(reminds)){
            ruleTypeRemindTime = getRecyclingRemindTime(recyclingRules,objectData);
        }
        if(objectData == null){
            return;
        }
        List<RuleTypeRemindTime> remindTimes = getNewRemindTimes(objectData, remindRules);
        if (ruleTypeRemindTime != null && ruleTypeRemindTime.getRemindTime() != null){
            remindTimes.add(ruleTypeRemindTime);
        }
        if (CollectionUtils.isEmpty(remindTimes)){
            deleteRemind(objectData.getTenantId(),objectData.getId());
        }
        RemindMessage newRemindMessage = RemindMessage.builder()
                .tenantId(objectData.getTenantId())
                .objectApiName(ApiNameEnum.ACCOUNT_OBJ.getApiName())
                .objectId(objectData.getId())
                .ruleTypeRemindTimes(remindTimes)
                .build();
        sendRemindTask(newRemindMessage);
    }

}
