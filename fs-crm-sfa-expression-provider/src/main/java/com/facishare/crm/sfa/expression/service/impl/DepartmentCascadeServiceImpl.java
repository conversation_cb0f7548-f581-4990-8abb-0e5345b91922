package com.facishare.crm.sfa.expression.service.impl;

import com.facishare.crm.sfa.expression.service.DepartmentCascadeService;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 部门级联查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
@Service
@Slf4j
public class DepartmentCascadeServiceImpl implements DepartmentCascadeService {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private EmployeeService employeeService;
    
    @Override
    public List<String> getAllSubDepartmentIds(String tenantId, List<String> departmentIds) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        
        Set<String> allDeptIds = Sets.newHashSet();
        
        for (String deptId : departmentIds) {
            if (StringUtils.isNotBlank(deptId)) {
                // 添加当前部门ID
                allDeptIds.add(deptId);
                
                // 获取所有子部门ID
                List<String> subDeptIds = getSubDepartmentIds(tenantId, deptId);
                allDeptIds.addAll(subDeptIds);
            }
        }
        
        return Lists.newArrayList(allDeptIds);
    }
    
    @Override
    public boolean isEmployeeInDepartment(String tenantId, String employeeId, List<String> departmentIds) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId) || CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }
        
        try {
            // 获取员工所属的部门ID
            String employeeDeptId = getEmployeeDepartmentId(tenantId, employeeId);
            if (StringUtils.isBlank(employeeDeptId)) {
                return false;
            }
            
            // 获取所有目标部门及其子部门ID
            List<String> allTargetDeptIds = getAllSubDepartmentIds(tenantId, departmentIds);
            
            // 判断员工部门是否在目标部门列表中
            return allTargetDeptIds.contains(employeeDeptId);
            
        } catch (Exception e) {
            log.error("Failed to check if employee {} is in departments {}, tenantId: {}", 
                    employeeId, departmentIds, tenantId, e);
            return false;
        }
    }
    
    @Override
    public boolean isDepartmentInParent(String tenantId, String targetDepartmentId, List<String> parentDepartmentIds) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(targetDepartmentId) || CollectionUtils.isEmpty(parentDepartmentIds)) {
            return false;
        }
        
        try {
            // 获取所有父部门及其子部门ID
            List<String> allParentDeptIds = getAllSubDepartmentIds(tenantId, parentDepartmentIds);
            
            // 判断目标部门是否在父部门列表中
            return allParentDeptIds.contains(targetDepartmentId);
            
        } catch (Exception e) {
            log.error("Failed to check if department {} is in parent departments {}, tenantId: {}", 
                    targetDepartmentId, parentDepartmentIds, tenantId, e);
            return false;
        }
    }
    
    /**
     * 获取指定部门的所有子部门ID（递归）
     *
     * @param tenantId 企业ID
     * @param departmentId 部门ID
     * @return 子部门ID列表
     */
    private List<String> getSubDepartmentIds(String tenantId, String departmentId) {
        try {
            // 使用部门服务获取所有子部门（包含递归）
            Map<String, List<String>> subDeptMap = departmentService.batchGetLowDepartmentIdsMap(
                    tenantId,
                    Lists.newArrayList(departmentId),
                    QueryDeptInfoByDeptIds.DeptStatusEnum.NORMAL,
                    false  // 不包含自身
            );

            List<String> subDeptIds = subDeptMap.get(departmentId);
            return subDeptIds != null ? subDeptIds : Lists.newArrayList();

        } catch (Exception e) {
            log.error("Failed to get sub department ids for dept: {}, tenantId: {}", departmentId, tenantId, e);
            return Lists.newArrayList();
        }
    }
    
    /**
     * 获取员工所属的部门ID
     *
     * @param tenantId 企业ID
     * @param employeeId 员工ID
     * @return 部门ID
     */
    private String getEmployeeDepartmentId(String tenantId, String employeeId) {
        try {
            // 使用员工服务获取员工的主部门ID
            // 注意：这里假设员工有主部门，如果员工可能属于多个部门，需要调整逻辑
            var employee = employeeService.getEmployeeById(tenantId, employeeId);
            if (employee != null && employee.getDepartmentId() != null) {
                return employee.getDepartmentId().toString();
            }
            return null;

        } catch (Exception e) {
            log.error("Failed to get employee department id for employee: {}, tenantId: {}", employeeId, tenantId, e);
            return null;
        }
    }
}
